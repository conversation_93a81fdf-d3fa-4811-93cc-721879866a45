import React from 'react';

// Simple Bar Chart Component (without external libraries)
const SimpleBarChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-warmGray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-warmGray-500">No data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.value));

  return (
    <div className={`bg-white/90 backdrop-blur-sm rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 p-8 border border-warmGray-100/50 ${className}`}>
      <h3 className="text-xl font-bold text-warmGray-800 mb-8 tracking-tight">{title}</h3>
      <div className="space-y-6">
        {data.map((item, index) => (
          <div key={index} className="group">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-semibold text-warmGray-700">
                {item.label}
              </span>
              <span className="text-sm font-bold text-warmGray-800">
                {item.value}
              </span>
            </div>
            <div className="relative">
              <div className="w-full bg-warmGray-100 rounded-full h-3 overflow-hidden shadow-inner">
                <div
                  className="h-full bg-gradient-to-r from-[#E8C4A0] via-[#DDB892] to-[#D4A574] rounded-full transition-all duration-1000 ease-out shadow-sm group-hover:shadow-md animate-slideIn"
                  style={{
                    width: `${(item.value / maxValue) * 100}%`,
                    animationDelay: `${index * 0.1}s`
                  }}
                />
              </div>
              <div className="absolute top-0 left-0 w-full h-full flex items-center justify-end pr-2">
                <div className="text-xs font-medium text-warmGray-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {Math.round((item.value / maxValue) * 100)}%
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

    </div>
  );
};

// Simple Line Chart Component
const SimpleLineChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-warmGray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-warmGray-500">No data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.value));
  const minValue = Math.min(...data.map(item => item.value));
  const range = maxValue - minValue || 1;

  return (
    <div className={`bg-white/90 backdrop-blur-sm rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 p-8 border border-warmGray-100/50 ${className}`}>
      <h3 className="text-xl font-bold text-warmGray-800 mb-8 tracking-tight">{title}</h3>
      <div className="relative h-56">
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-warmGray-500 font-medium">
          <span className="bg-white/80 px-2 py-1 rounded">{maxValue}</span>
          <span className="bg-white/80 px-2 py-1 rounded">{Math.round((maxValue + minValue) / 2)}</span>
          <span className="bg-white/80 px-2 py-1 rounded">{minValue}</span>
        </div>

        {/* Chart area */}
        <div className="ml-12 h-full relative">
          <svg className="w-full h-full" viewBox="0 0 400 200">
            {/* Subtle grid lines */}
            {[0, 1, 2, 3, 4].map(i => (
              <line
                key={i}
                x1="0"
                y1={i * 40}
                x2="400"
                y2={i * 40}
                stroke="#f8fafc"
                strokeWidth="1"
                opacity="0.6"
              />
            ))}

            {/* Gradient definition */}
            <defs>
              <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#E8C4A0" />
                <stop offset="50%" stopColor="#DDB892" />
                <stop offset="100%" stopColor="#D4A574" />
              </linearGradient>
              <filter id="glow">
                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>

            {/* Area under the line */}
            <path
              d={`M ${data.map((item, index) => {
                const x = (index / (data.length - 1)) * 400;
                const y = 200 - ((item.value - minValue) / range) * 200;
                return `${index === 0 ? 'M' : 'L'} ${x},${y}`;
              }).join(' ')} L 400,200 L 0,200 Z`}
              fill="url(#lineGradient)"
              opacity="0.1"
            />

            {/* Data line */}
            <polyline
              fill="none"
              stroke="url(#lineGradient)"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
              filter="url(#glow)"
              points={data.map((item, index) => {
                const x = (index / (data.length - 1)) * 400;
                const y = 200 - ((item.value - minValue) / range) * 200;
                return `${x},${y}`;
              }).join(' ')}
              className="animate-drawLine"
            />

            {/* Data points */}
            {data.map((item, index) => {
              const x = (index / (data.length - 1)) * 400;
              const y = 200 - ((item.value - minValue) / range) * 200;
              return (
                <g key={index}>
                  <circle
                    cx={x}
                    cy={y}
                    r="6"
                    fill="white"
                    stroke="url(#lineGradient)"
                    strokeWidth="3"
                    className="hover:r-8 transition-all duration-300 cursor-pointer animate-fadeInPoint"
                    style={{
                      opacity: '0',
                      animationDelay: `${index * 0.1 + 1}s`
                    }}
                  />
                  <circle
                    cx={x}
                    cy={y}
                    r="3"
                    fill="url(#lineGradient)"
                    className="animate-fadeInPoint"
                    style={{
                      opacity: '0',
                      animationDelay: `${index * 0.1 + 1.2}s`
                    }}
                  />
                </g>
              );
            })}
          </svg>
        </div>

        {/* X-axis labels */}
        <div className="ml-12 mt-4 flex justify-between text-xs text-warmGray-600 font-medium">
          {data.map((item, index) => (
            <span key={index} className="bg-white/60 px-2 py-1 rounded">{item.label}</span>
          ))}
        </div>
      </div>

    </div>
  );
};

// Rating Distribution Chart
const RatingDistributionChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-warmGray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-warmGray-500">No ratings data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.count));
  const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#16a34a']; // Red to Green

  return (
    <div className={`bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-warmGray-800 mb-6">{title}</h3>
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center space-x-3">
            <div className="flex items-center space-x-1 w-16">
              <span className="text-sm font-medium text-warmGray-700">{item.rating}</span>
              <span className="text-yellow-400">★</span>
            </div>
            <div className="flex-1 bg-warmGray-200 rounded-full h-6 relative overflow-hidden">
              <div
                className="h-full rounded-full transition-all duration-700 ease-out"
                style={{ 
                  width: `${(item.count / maxValue) * 100}%`,
                  backgroundColor: colors[index] || '#E8C4A0'
                }}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xs font-medium text-white">
                  {item.count}
                </span>
              </div>
            </div>
            <div className="w-12 text-xs text-warmGray-500">
              {maxValue > 0 ? Math.round((item.count / maxValue) * 100) : 0}%
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export { SimpleBarChart, SimpleLineChart, RatingDistributionChart };
